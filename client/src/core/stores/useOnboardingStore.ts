import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface OnboardingState {
  // 已完成引导的页面
  completedPages: string[];
  // 是否正在运行引导
  isRunning: boolean;

  // Actions
  markPageCompleted: (page: string) => void;
  isPageCompleted: (page: string) => boolean;
  startTour: () => void;
  stopTour: () => void;
}

/**
 * 简化的新手引导状态管理
 */
export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set, get) => ({
      completedPages: [],
      isRunning: false,

      markPageCompleted: (page) => {
        const { completedPages } = get();
        if (!completedPages.includes(page)) {
          set({
            completedPages: [...completedPages, page],
          });
        }
      },

      isPageCompleted: (page) => {
        const { completedPages } = get();
        return completedPages.includes(page);
      },

      startTour: () => set({
        isRunning: true,
      }),

      stopTour: () => set({
        isRunning: false,
      }),
    }),
    {
      name: 'onboarding-storage',
      storage: createJSONStorage(() => localStorage),
    },
  ),
);
