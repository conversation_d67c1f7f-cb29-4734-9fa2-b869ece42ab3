import { create } from 'zustand';
import { devtools, persist, createJSONStorage } from 'zustand/middleware';

interface OnboardingState {
  // 是否已完成引导
  isCompleted: boolean;
  // 是否正在运行引导
  isRunning: boolean;
  // 当前步骤索引
  currentStepIndex: number;
  // 当前页面的引导步骤
  currentPageSteps: string[];
  // 是否应该在登录后自动开始
  shouldStartAfterLogin: boolean;
  
  // Actions
  setCompleted: (completed: boolean) => void;
  setRunning: (running: boolean) => void;
  setCurrentStepIndex: (index: number) => void;
  setCurrentPageSteps: (steps: string[]) => void;
  setShouldStartAfterLogin: (should: boolean) => void;
  startTour: () => void;
  stopTour: () => void;
  restartTour: () => void;
  nextStep: () => void;
  prevStep: () => void;
  reset: () => void;
}

/**
 * 新手引导状态管理
 */
export const useOnboardingStore = create<OnboardingState>()(
  devtools(
    persist(
      (set, get) => ({
        isCompleted: false,
        isRunning: false,
        currentStepIndex: 0,
        currentPageSteps: [],
        shouldStartAfterLogin: true,

        setCompleted: (completed) => set({
          isCompleted: completed,
          isRunning: completed ? false : get().isRunning,
        }, false, 'setCompleted'),

        setRunning: (running) => set({
          isRunning: running,
        }, false, 'setRunning'),

        setCurrentStepIndex: (index) => set({
          currentStepIndex: index,
        }, false, 'setCurrentStepIndex'),

        setCurrentPageSteps: (steps) => set({
          currentPageSteps: steps,
        }, false, 'setCurrentPageSteps'),

        setShouldStartAfterLogin: (should) => set({
          shouldStartAfterLogin: should,
        }, false, 'setShouldStartAfterLogin'),

        startTour: () => set({
          isRunning: true,
          currentStepIndex: 0,
        }, false, 'startTour'),

        stopTour: () => set({
          isRunning: false,
        }, false, 'stopTour'),

        restartTour: () => set({
          isRunning: true,
          currentStepIndex: 0,
          isCompleted: false,
        }, false, 'restartTour'),

        nextStep: () => {
          const { currentStepIndex, currentPageSteps } = get();
          if (currentStepIndex < currentPageSteps.length - 1) {
            set({
              currentStepIndex: currentStepIndex + 1,
            }, false, 'nextStep');
          }
        },

        prevStep: () => {
          const { currentStepIndex } = get();
          if (currentStepIndex > 0) {
            set({
              currentStepIndex: currentStepIndex - 1,
            }, false, 'prevStep');
          }
        },

        reset: () => set({
          isCompleted: false,
          isRunning: false,
          currentStepIndex: 0,
          currentPageSteps: [],
          shouldStartAfterLogin: true,
        }, false, 'reset'),
      }),
      {
        name: 'onboarding-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          isCompleted: state.isCompleted,
          shouldStartAfterLogin: state.shouldStartAfterLogin,
        }),
      },
    ),
    {
      name: 'onboarding-store',
    },
  ),
);

export const getOnboardingStore = () => useOnboardingStore.getState();
