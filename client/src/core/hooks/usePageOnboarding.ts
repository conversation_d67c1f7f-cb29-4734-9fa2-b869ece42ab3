import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from '~/core/stores/useOnboardingStore';

/**
 * 页面首次访问引导钩子
 * 当用户首次访问某个页面时，自动触发引导
 */
export const usePageOnboarding = (page: string) => {
  const location = useLocation();
  const { startTour, isPageCompleted } = useOnboardingStore();
  
  useEffect(() => {
    // 如果页面未完成引导，则启动引导
    if (!isPageCompleted(page)) {
      // 延迟一下确保页面元素已加载
      const timer = setTimeout(() => {
        startTour();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [page, isPageCompleted, startTour]);
};

/**
 * 登录后主页引导钩子
 * 用于登录成功后的主页引导
 */
export const useLoginOnboarding = () => {
  const { startTour, isPageCompleted } = useOnboardingStore();
  
  const triggerMainOnboarding = () => {
    if (!isPageCompleted('main')) {
      setTimeout(() => {
        startTour();
      }, 1500); // 稍长的延迟确保页面完全加载
    }
  };
  
  return { triggerMainOnboarding };
};
