import React, { useEffect, useCallback } from 'react';
import Joyride, { CallBackProps, STATUS, EVENTS, ACTIONS } from 'react-joyride';
import { useLocation, useNavigate } from 'react-router-dom';
import { useOnboardingStore } from '~/core/stores/useOnboardingStore';
import { getStepsByPath, getStepIds } from './tourSteps';
import { useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

const OnboardingTour: React.FC = () => {
  const { isRunning } = useOnboardingStore();

  // 简化版本，暂时只返回 null 来测试是否是组件导致的问题
  if (!isRunning) {
    return null;
  }

  return (
    <div style={{ position: 'fixed', top: 10, right: 10, background: 'red', color: 'white', padding: '5px', zIndex: 9999 }}>
      引导组件已加载
    </div>
  );

};

export default OnboardingTour;
