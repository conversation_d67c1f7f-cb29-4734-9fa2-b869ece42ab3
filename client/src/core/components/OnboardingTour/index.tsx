import React, { useEffect, useCallback } from 'react';
import Joyride, { CallBackProps, STATUS, EVENTS, ACTIONS } from 'react-joyride';
import { useLocation, useNavigate } from 'react-router-dom';
import { useOnboardingStore } from '~/core/stores/useOnboardingStore';
import { getStepsByPath, getStepIds } from './tourSteps';
import { useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

const OnboardingTour: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const {
    isRunning,
    currentStepIndex,
    setRunning,
    setCurrentStepIndex,
    setCurrentPageSteps,
    setCompleted,
    stopTour,
  } = useOnboardingStore();

  // 获取当前页面的引导步骤
  const currentSteps = getStepsByPath(location.pathname);
  const currentStepIds = getStepIds(currentSteps);

  // 更新当前页面步骤
  useEffect(() => {
    setCurrentPageSteps(currentStepIds);
  }, [location.pathname, setCurrentPageSteps, currentStepIds]);

  // 处理引导回调
  const handleJoyrideCallback = useCallback((data: CallBackProps) => {
    const { status, type, index, action } = data;

    if (type === EVENTS.STEP_AFTER || type === EVENTS.TARGET_NOT_FOUND) {
      // 更新步骤索引
      setCurrentStepIndex(index + (action === ACTIONS.PREV ? -1 : 1));
    } else if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      // 引导完成或跳过
      setCompleted(true);
      setRunning(false);
    } else if (status === STATUS.ERROR) {
      // 引导出错，停止引导
      console.warn('Joyride error:', data);
      stopTour();
    }
  }, [setCurrentStepIndex, setCompleted, setRunning, stopTour]);

  // 自定义样式
  const joyrideStyles = {
    options: {
      primaryColor: isDark ? theme.colors.blue[6] : theme.colors.blue[7],
      backgroundColor: isDark ? theme.colors.dark[7] : theme.white,
      textColor: isDark ? theme.colors.gray[1] : theme.colors.gray[8],
      overlayColor: isDark ? 'rgba(0, 0, 0, 0.7)' : 'rgba(0, 0, 0, 0.5)',
      spotlightShadow: isDark ? '0 0 15px rgba(255, 255, 255, 0.3)' : '0 0 15px rgba(0, 0, 0, 0.3)',
      beaconSize: 36,
      zIndex: 10000,
    },
    tooltip: {
      backgroundColor: isDark ? theme.colors.dark[6] : theme.white,
      borderRadius: 8,
      color: isDark ? theme.colors.gray[1] : theme.colors.gray[8],
      fontSize: 14,
      padding: 16,
      boxShadow: isDark 
        ? '0 4px 20px rgba(0, 0, 0, 0.5)' 
        : '0 4px 20px rgba(0, 0, 0, 0.15)',
    },
    tooltipContainer: {
      textAlign: 'left' as const,
    },
    tooltipTitle: {
      color: isDark ? theme.colors.gray[0] : theme.colors.gray[9],
      fontSize: 16,
      fontWeight: 'bold',
      margin: '0 0 8px 0',
    },
    tooltipContent: {
      color: isDark ? theme.colors.gray[2] : theme.colors.gray[7],
      fontSize: 14,
      lineHeight: 1.5,
      margin: 0,
    },
    buttonNext: {
      backgroundColor: isDark ? theme.colors.blue[6] : theme.colors.blue[7],
      borderRadius: 6,
      color: theme.white,
      fontSize: 14,
      fontWeight: 500,
      padding: '8px 16px',
      border: 'none',
      cursor: 'pointer',
    },
    buttonBack: {
      backgroundColor: 'transparent',
      border: `1px solid ${isDark ? theme.colors.gray[6] : theme.colors.gray[4]}`,
      borderRadius: 6,
      color: isDark ? theme.colors.gray[3] : theme.colors.gray[7],
      fontSize: 14,
      fontWeight: 500,
      padding: '8px 16px',
      cursor: 'pointer',
      marginRight: 8,
    },
    buttonSkip: {
      backgroundColor: 'transparent',
      border: 'none',
      color: isDark ? theme.colors.gray[5] : theme.colors.gray[6],
      fontSize: 14,
      fontWeight: 500,
      padding: '8px 16px',
      cursor: 'pointer',
    },
    buttonClose: {
      backgroundColor: 'transparent',
      border: 'none',
      color: isDark ? theme.colors.gray[5] : theme.colors.gray[6],
      fontSize: 18,
      fontWeight: 'bold',
      padding: 4,
      cursor: 'pointer',
      position: 'absolute' as const,
      right: 8,
      top: 8,
    },
    spotlight: {
      borderRadius: 4,
    },
  };

  // 自定义文本
  const locale = {
    back: '上一步',
    close: '关闭',
    last: '完成',
    next: '下一步',
    skip: '跳过',
  };

  if (!isRunning || currentSteps.length === 0) {
    return null;
  }

  return (
    <Joyride
      steps={currentSteps}
      run={isRunning}
      stepIndex={currentStepIndex}
      callback={handleJoyrideCallback}
      continuous={true}
      showProgress={true}
      showSkipButton={true}
      styles={joyrideStyles}
      locale={locale}
      disableOverlayClose={true}
      disableScrollParentFix={true}
      floaterProps={{
        disableAnimation: false,
      }}
      tooltipComponent={({ tooltipProps, primaryProps, backProps, skipProps, closeProps, step, index, size }) => (
        <div style={joyrideStyles.tooltip}>
          {/* 关闭按钮 */}
          <button {...closeProps} style={joyrideStyles.buttonClose}>
            ×
          </button>
          
          {/* 内容 */}
          <div style={joyrideStyles.tooltipContainer}>
            {step.content}
          </div>
          
          {/* 进度指示器 */}
          <div style={{ 
            margin: '16px 0 12px 0', 
            fontSize: 12, 
            color: isDark ? theme.colors.gray[5] : theme.colors.gray[6],
            textAlign: 'center' as const
          }}>
            {index + 1} / {size}
          </div>
          
          {/* 按钮组 */}
          <div style={{ 
            display: 'flex', 
            justifyContent: 'space-between', 
            alignItems: 'center',
            marginTop: 16 
          }}>
            <div>
              {index > 0 && (
                <button {...backProps} style={joyrideStyles.buttonBack}>
                  {locale.back}
                </button>
              )}
            </div>
            
            <div style={{ display: 'flex', gap: 8 }}>
              <button {...skipProps} style={joyrideStyles.buttonSkip}>
                {locale.skip}
              </button>
              <button {...primaryProps} style={joyrideStyles.buttonNext}>
                {index === size - 1 ? locale.last : locale.next}
              </button>
            </div>
          </div>
        </div>
      )}
    />
  );
};

export default OnboardingTour;
