import React, { useEffect } from 'react';
import Joyride, { CallBackProps, STATUS } from 'react-joyride';
import { useLocation } from 'react-router-dom';
import { useOnboardingStore } from '~/core/stores/useOnboardingStore';
import { getStepsByPage, getPageTypeFromPath } from './tourSteps';

interface OnboardingTourProps {
  page?: string;
}

const OnboardingTour: React.FC<OnboardingTourProps> = ({ page }) => {
  const location = useLocation();
  const { isRunning, stopTour, markPageCompleted, isPageCompleted } = useOnboardingStore();

  // 确定当前页面
  const currentPage = page || getPageTypeFromPath(location.pathname);

  // 获取当前页面的引导步骤
  const steps = getStepsByPage(currentPage);

  // 检查是否需要显示引导
  const shouldShowTour = isRunning && steps.length > 0 && !isPageCompleted(currentPage);

  // 处理引导回调
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status } = data;

    if (status === STATUS.FINISHED || status === STATUS.SKIPPED) {
      markPageCompleted(currentPage);
      stopTour();
    }
  };

  if (!shouldShowTour) {
    return null;
  }

  return (
    <Joyride
      steps={steps}
      run={true}
      continuous={true}
      showSkipButton={true}
      callback={handleJoyrideCallback}
      styles={{
        options: {
          primaryColor: '#228be6',
          zIndex: 10000,
        },
      }}
      locale={{
        back: '上一步',
        close: '关闭',
        last: '完成',
        next: '下一步',
        skip: '跳过',
      }}
    />
  );
};



export default OnboardingTour;
