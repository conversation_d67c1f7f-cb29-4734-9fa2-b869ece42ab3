# 新手引导功能实现说明

## 功能概述

本功能使用 react-joyride 库为客户端界面添加了完整的新手引导系统，帮助用户了解如何使用网站的各种功能。

## 主要特性

### 1. 引导内容设计
- **步骤名称与描述分离**：每个引导步骤都有清晰的标题和详细说明
- **简明清晰**：内容简洁易懂，避免冗长的描述
- **功能合并**：将智能体、工作流、数字人等相似功能合并介绍

### 2. 多页面支持
- **主页引导**：介绍主要功能区域（对话、AI服务、课程、知识库等）
- **对话页面**：详细介绍如何输入内容、发送消息、查看历史
- **智能体市场**：介绍分类浏览和智能体选择
- **课程页面**：介绍课程分类和课程选择

### 3. 触发机制
- **登录后自动触发**：用户首次登录完成后自动开始引导
- **手动重启**：在设置中提供"重新开始引导"按钮
- **智能导航**：点击重新开始时自动导航到聊天页面

### 4. 状态管理
- **持久化存储**：引导完成状态保存在 localStorage
- **避免重复**：已完成引导的用户不会重复触发
- **灵活控制**：支持手动重置和重新开始

## 技术实现

### 核心组件

1. **OnboardingTour** (`client/src/core/components/OnboardingTour/index.tsx`)
   - 主要的引导组件，使用 react-joyride 实现
   - 支持自定义样式和主题适配
   - 处理引导流程的各种回调事件

2. **useOnboardingStore** (`client/src/core/stores/useOnboardingStore.ts`)
   - 使用 Zustand 管理引导状态
   - 支持持久化存储
   - 提供完整的状态控制方法

3. **tourSteps** (`client/src/core/components/OnboardingTour/tourSteps.ts`)
   - 定义各页面的引导步骤
   - 根据路径动态返回对应步骤
   - 支持自定义内容和样式

### 集成点

1. **Desktop 布局** (`client/src/core/components/Layouts/Desktop.tsx`)
   - 集成 OnboardingTour 组件
   - 添加必要的 data-tour 属性
   - 支持菜单项的引导标识

2. **登录钩子** (`client/src/user/authentication/hooks/useLogin.ts`)
   - 登录成功后触发引导
   - 延迟启动确保页面加载完成
   - 避免重复触发机制

3. **设置组件** (`client/src/core/components/Settings/Desktop.tsx`)
   - 添加"重新开始引导"按钮
   - 点击时导航到聊天页面并启动引导
   - 提供用户友好的重启入口

### 页面适配

各个页面都添加了相应的 data-tour 属性：

- **聊天页面**：输入框、发送按钮、对话历史
- **智能体市场**：分类选择器、智能体卡片
- **课程页面**：课程分类、课程卡片
- **主布局**：Logo、菜单项、历史记录、设置

## 使用方法

### 用户体验流程

1. **首次登录**：用户登录成功后，系统自动导航到聊天页面并启动引导
2. **引导过程**：用户可以按步骤浏览各功能介绍，支持前进、后退、跳过
3. **完成引导**：引导完成后状态会被保存，避免重复触发
4. **重新学习**：用户可在设置中点击"重新开始引导"重新体验

### 开发者使用

1. **添加新的引导步骤**：
   ```typescript
   // 在 tourSteps.ts 中添加新步骤
   {
     target: '[data-tour="new-feature"]',
     content: (
       <div>
         <h3>新功能标题</h3>
         <p>新功能描述</p>
       </div>
     ),
     placement: 'bottom',
   }
   ```

2. **在组件中添加引导标识**：
   ```jsx
   <div data-tour="new-feature">
     {/* 需要引导的元素 */}
   </div>
   ```

3. **控制引导状态**：
   ```typescript
   const { startTour, stopTour, restartTour } = useOnboardingStore();
   ```

## 样式定制

引导组件支持主题适配，会根据当前的明暗主题自动调整样式：

- **明亮主题**：白色背景，深色文字
- **暗色主题**：深色背景，浅色文字
- **交互元素**：按钮、进度条等都会适配主题色彩

## 注意事项

1. **性能优化**：引导组件只在需要时渲染，避免不必要的性能开销
2. **错误处理**：包含完整的错误处理机制，引导出错时会自动停止
3. **可访问性**：支持键盘导航和屏幕阅读器
4. **响应式**：在不同设备尺寸下都能正常工作

## 未来扩展

1. **多语言支持**：可以扩展支持多种语言的引导内容
2. **个性化引导**：根据用户角色或权限显示不同的引导内容
3. **引导分析**：收集用户引导完成情况的统计数据
4. **动态引导**：根据用户行为动态调整引导内容
