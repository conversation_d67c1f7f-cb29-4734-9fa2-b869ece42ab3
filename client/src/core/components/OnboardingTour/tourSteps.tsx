import { Step } from 'react-joyride';

// 主页引导步骤
export const mainPageSteps: Step[] = [
  {
    target: '[data-tour="logo"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          欢迎来到 AIZY
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          这里是您的AI助手平台，让我们开始探索各种强大的功能吧！
        </p>
      </div>
    ),
    placement: 'bottom',
    disableBeacon: true,
  },
  {
    target: '[data-tour="chat-menu"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          智能对话
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          点击这里开始与AI进行对话，获得各种问题的解答和帮助。
        </p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="ai-services-menu"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          AI服务中心
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          这里包含智能体、工作流和数字人等AI服务，您可以选择不同的AI助手来完成特定任务。
        </p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="course-menu"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          学习课程
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          浏览和学习各种AI相关课程，提升您的技能和知识。
        </p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="knowledge-menu"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          知识库
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          管理和查询您的个人知识库，让AI更好地理解您的需求。
        </p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="history-section"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          对话历史
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          这里显示您的历史对话记录，方便您随时回顾和继续之前的对话。
        </p>
      </div>
    ),
    placement: 'right',
  },
  {
    target: '[data-tour="settings-menu"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          个人设置
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          在这里可以管理您的个人信息、查看使用情况，以及重新开始引导教程。
        </p>
      </div>
    ),
    placement: 'right',
  },
];

// 对话页面引导步骤
export const chatPageSteps: Step[] = [
  {
    target: '[data-tour="chat-input"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          输入对话内容
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          在这里输入您想要询问的问题或需要帮助的内容。
        </p>
      </div>
    ),
    placement: 'top',
  },
  {
    target: '[data-tour="chat-send"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          发送消息
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          点击发送按钮或按 Enter 键发送消息。按 Shift+Enter 可以换行。
        </p>
      </div>
    ),
    placement: 'top',
  },
  {
    target: '[data-tour="chat-history"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          对话记录
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          这里显示您与AI的对话内容，支持滚动查看历史消息。
        </p>
      </div>
    ),
    placement: 'bottom',
  },
];

// 智能体市场引导步骤
export const agentMarketplaceSteps: Step[] = [
  {
    target: '[data-tour="agent-categories"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          智能体分类
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          浏览不同类别的智能体，找到最适合您需求的AI助手。
        </p>
      </div>
    ),
    placement: 'bottom',
  },
  {
    target: '[data-tour="agent-card"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          选择智能体
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          点击智能体卡片查看详情，或直接购买使用。每个智能体都有特定的专业领域。
        </p>
      </div>
    ),
    placement: 'top',
  },
];

// 课程页面引导步骤
export const coursePageSteps: Step[] = [
  {
    target: '[data-tour="course-categories"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          课程分类
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          选择您感兴趣的课程类别，系统学习相关知识。
        </p>
      </div>
    ),
    placement: 'bottom',
  },
  {
    target: '[data-tour="course-card"]',
    content: (
      <div>
        <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', fontWeight: 'bold' }}>
          课程详情
        </h3>
        <p style={{ margin: 0, fontSize: '14px', lineHeight: '1.5' }}>
          查看课程介绍、学习时长和价格信息，选择适合的课程开始学习。
        </p>
      </div>
    ),
    placement: 'top',
  },
];

// 根据路径获取对应的引导步骤
export const getStepsByPath = (pathname: string): Step[] => {
  if (pathname === '/chat') {
    return chatPageSteps;
  } else if (pathname.startsWith('/agent/marketplace')) {
    return agentMarketplaceSteps;
  } else if (pathname.startsWith('/course')) {
    return coursePageSteps;
  } else {
    return mainPageSteps;
  }
};

// 获取步骤的唯一标识符
export const getStepIds = (steps: Step[]): string[] => {
  return steps.map((step, index) => `${step.target}-${index}`);
};
