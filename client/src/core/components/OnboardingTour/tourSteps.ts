import { Step } from 'react-joyride';

// 简化的引导步骤定义
export const tourSteps: { [key: string]: Step[] } = {
  // 主页引导
  main: [
    {
      target: '[data-tour="chat-menu"]',
      content: '点击这里开始与AI进行对话，获得各种问题的解答和帮助。',
      placement: 'right',
      disableBeacon: true,
    },
    {
      target: '[data-tour="ai-services-menu"]',
      content: '这里包含智能体、工作流和数字人等AI服务，选择不同的AI助手完成特定任务。',
      placement: 'right',
    },
    {
      target: '[data-tour="course-menu"]',
      content: '浏览和学习各种AI相关课程，提升您的技能和知识。',
      placement: 'right',
    },
  ],

  // 对话页面引导
  chat: [
    {
      target: '[data-tour="chat-input"]',
      content: '在这里输入您想要询问的问题。按 Shift+Enter 可以换行。',
      placement: 'top',
      disableBeacon: true,
    },
    {
      target: '[data-tour="chat-send"]',
      content: '点击发送按钮或按 Enter 键发送消息。',
      placement: 'top',
    },
  ],

  // 智能体市场引导
  agent: [
    {
      target: '[data-tour="agent-categories"]',
      content: '浏览不同类别的智能体，找到最适合您需求的AI助手。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '[data-tour="agent-card"]',
      content: '点击智能体卡片查看详情或购买使用。',
      placement: 'top',
    },
  ],

  // 课程页面引导
  course: [
    {
      target: '[data-tour="course-categories"]',
      content: '选择您感兴趣的课程类别，系统学习相关知识。',
      placement: 'bottom',
      disableBeacon: true,
    },
    {
      target: '[data-tour="course-card"]',
      content: '查看课程介绍和价格信息，选择适合的课程开始学习。',
      placement: 'top',
    },
  ],
};

// 根据页面获取引导步骤
export const getStepsByPage = (page: string): Step[] => {
  return tourSteps[page] || [];
};
