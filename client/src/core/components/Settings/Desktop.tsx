/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useState } from 'react';
import { Stack, Box, UnstyledButton, Text, Menu, Group, Select, useMantineTheme, Divider } from '@mantine/core';
import { RiSettings3Line, RiSunLine, RiTranslate, RiLogoutBoxRLine, RiQuestionLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import { useNavigate } from 'react-router-dom';
import { useUserStore } from '~/user/core/store';
import { useMigrationStore } from '~/core/features/useMigrationStore';
import { useOnboardingStore } from '~/core/stores/useOnboardingStore';
import { db } from '~/agent/chat/db';
import { notifications } from '@mantine/notifications';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const navigate = useNavigate();
  const { clearUser } = useUserStore();
  const { reset: resetMigration } = useMigrationStore();

  const textColor = isDark ? '#E6E6E6' : 'rgba(56, 56, 56, 1)';
  const hoverBackgroundColor = isDark
    ? theme.colors.gray[8]
    : 'linear-gradient(0deg, rgba(247, 248, 255, 1), rgba(247, 248, 255, 1)), rgba(243, 245, 255, 1)';
  const activeBackgroundColor = 'rgba(73, 81, 235, 1)'; // Indigo color for active item
  const activeTextColor = '#FFFFFF';
  const labelColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const boxShadow = isDark ? '0px 3px 15px rgba(23, 16, 3, 0.5)' : '0px 3px 15px rgba(232, 239, 252, 0.5)';

  const [colorScheme, setColorScheme] = useState<string | null>('light');
  const [language, setLanguage] = useState<string | null>('zh-CN');

  const { clearUser } = useUserStore();
  const { reset: resetMigration } = useMigrationStore();
  const { restartTour } = useOnboardingStore();
  const navigate = useNavigate();

  // 重新开始引导处理函数
  const handleRestartTour = () => {
    // 导航到首页
    navigate('/chat');
    // 延迟一下确保页面加载完成后再开始引导
    setTimeout(() => {
      restartTour();
    }, 500);
  };

  // 退出登录处理函数
  const handleLogout = async () => {
    try {
      // 清空会话历史数据
      await db.clearAll();

      // 重置数据迁移状态
      resetMigration();

      // 清除用户信息
      clearUser();

      // 显示成功提示
      notifications.show({
        title: '退出成功',
        message: '您已成功退出登录',
        color: 'green',
      });

      // 跳转到登录页面
      navigate('/login');
    } catch (error) {
      console.error('退出登录失败:', error);
      notifications.show({
        title: '退出失败',
        message: '退出登录时发生错误，请重试',
        color: 'red',
      });
    }
  };

  const colorSchemeInfo = {
    label: '界面主题',
    key: 'colorScheme',
    icon: RiSunLine,
    options: [
      //{ label: '跟随系统', value: 'auto' },
      { label: '明', value: 'light' },
      // { label: '暗', value: 'dark' },
    ],
  };
  const languageInfo = {
    label: '语言',
    key: 'language',
    icon: RiTranslate,
    options: [
      { label: '简体中文', value: 'zh-CN' },
      // { label: 'English', value: 'en-US' },
    ],
  };

  return (
    <Menu
      position="right-end"
      styles={{
        dropdown: {
          padding: '8px',
          border: 0,
          borderRadius: '12px',
          boxShadow: boxShadow,
        },
        item: {
          borderRadius: '8px',
        },
      }}
    >
      <Menu.Target>
        <Box position="relative" w="100%" h="45px" style={{ color: textColor }}>
          <UnstyledButton
            className={`flex items-center w-full h-full rounded-[10px]`}
            px={16}
            css={css`
              &:hover {
                background: ${hoverBackgroundColor};
              }
              &.active {
                background: ${activeBackgroundColor};
                color: ${activeTextColor};
              }
            `}
          >
            <RiSettings3Line size={20} />
            <Text className="flex-auto leading-[18px] fw-700" ml={16} fz={16}>
              设置
            </Text>
          </UnstyledButton>
        </Box>
      </Menu.Target>

      <Menu.Dropdown p={16}>
        <Stack gap={16}>
          <Group align="center" justify="space-between">
            <Group align="center" gap={8}>
              <colorSchemeInfo.icon size={18} color={labelColor} />
              <Text fz={16} fw={500} lh="24px" c={labelColor}>
                {colorSchemeInfo.label}
              </Text>
            </Group>
            <Select
              w={100}
              variant="filled"
              size="xs"
              value={colorScheme}
              onChange={setColorScheme}
              data={colorSchemeInfo.options}
            />
          </Group>
          <Group align="center" justify="space-between">
            <Group align="center" gap={8}>
              <languageInfo.icon size={18} color={labelColor} />
              <Text fz={16} fw={500} lh="24px" c={labelColor}>
                {languageInfo.label}
              </Text>
            </Group>
            <Select w={100} variant="filled" size="xs" value={language} onChange={setLanguage} data={languageInfo.options} />
          </Group>

          {/* 分割线 */}
          <Divider />

          {/* 重新开始引导 */}
          <UnstyledButton
            onClick={handleRestartTour}
            css={css`
              width: 100%;
              padding: 8px 12px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              gap: 8px;
              color: ${isDark ? '#74c0fc' : '#1971c2'};
              &:hover {
                background: ${isDark ? 'rgba(116, 192, 252, 0.1)' : 'rgba(25, 113, 194, 0.1)'};
              }
            `}
          >
            <RiQuestionLine size={18} />
            <Text fz={16} fw={500} lh="24px">
              重新开始引导
            </Text>
          </UnstyledButton>

          {/* 退出登录 */}
          <UnstyledButton
            onClick={handleLogout}
            css={css`
              width: 100%;
              padding: 8px 12px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              gap: 8px;
              color: ${isDark ? '#ff6b6b' : '#e03131'};
              &:hover {
                background: ${isDark ? 'rgba(255, 107, 107, 0.1)' : 'rgba(224, 49, 49, 0.1)'};
              }
            `}
          >
            <RiLogoutBoxRLine size={18} />
            <Text fz={16} fw={500} lh="24px">
              退出登录
            </Text>
          </UnstyledButton>
        </Stack>
      </Menu.Dropdown>
    </Menu>
  );
};

export default Desktop;
